<?php

namespace App\Controller;

use App\Client\CompaniesMadeSimple;
use App\Client\DeveloperList;
use App\Client\MatchClient;
use App\Client\PubSub;
use App\Client\Storage;
use App\Client\JWTAuthorization;
use App\Dto\Topic;
use App\Dto\Result;
use App\Entity\GlobalMailroomStatus;
use App\Entity\PostItem\PostItem;
use App\Entity\PostItem\PostItemsDetail;
use App\Entity\StatusMessage;
use App\Entity\TransitFee;
use App\Model\AddPostItem;
use App\Model\PostItemDetail;
use App\Model\PostItemEventLog;
use App\Repository\GlobalMailroomStatusRepository;
use App\Repository\PostItem\PostItemRepository;
use App\Repository\PostItem\PostItemsDetailRepository;
use App\Repository\StatusMessageRepository;
use DateTime;
use DateTimeZone;
use Doctrine\DBAL\Types\ConversionException;
use Doctrine\Persistence\ManagerRegistry;
use Doctrine\Persistence\ObjectRepository;
use Exception;
use Google\Cloud\PubSub\Message;
use InvalidArgumentException;
use Monolog\Logger;
use phpDocumentor\Reflection\Type;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Process\Exception\ProcessFailedException;
use Symfony\Component\Process\Process;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Class ApiController
 * todo authentication
 * todo split development and live logic
 */
class ApiController extends AbstractController
{
    private $firebaseConf;

    private const GET_POST_ITEMS_API_DEFAULT_MAX_AGE = 91;
    private const ORDER_ID_DEFAULT_KEY = 'order_id';
    private const LAST_EMAIL_SENT_DEFAULT_KEY = 'last_email_sent';

    private CompaniesMadeSimple $companiesMadeSimple;
    private PubSub $pubSub;
    private Storage $storage;
    private ManagerRegistry $doctrine;
    private JWTAuthorization $JWTAuthorization;
    private DeveloperList $developerList;
    private MatchClient $matchClient;
    private Logger $logger;

    /**
     * PostItemsController constructor
     *
     * @param ManagerRegistry $doctrine
     */

    public function __construct(
        ManagerRegistry $doctrine,
        PubSub $pubSub,
        Storage $storage,
        CompaniesMadeSimple $companiesMadeSimple,
        JWTAuthorization $JWTAuthorization,
        DeveloperList $developerList,
        MatchClient $matchClient,
        ?Logger $logger = null,
    )
    {
        $this->pubSub = $pubSub;
        $this->storage = $storage;
        $this->doctrine = $doctrine;
        $this->companiesMadeSimple = $companiesMadeSimple;
        $this->JWTAuthorization = $JWTAuthorization;
        $this->developerList = $developerList;
        $this->matchClient = $matchClient;

        $this->firebaseConf = [
            'key' => $_ENV['FIREBASE_KEY'],
            'project_id' => $_ENV['FIREBASE_PROJECT_ID'],
            'iss_pattern' => $_ENV['FIREBASE_ISS_PATTERN'],
            'public_keys_link' => $_ENV['FIREBASE_PUBLIC_KEYS_LINK'],
        ];

        $this->logger = $this->logger ?? new Logger('add_post_item_model');
    }

    /**
     * @Route("/api/session/", methods={"POST"}, name="api_session")
     * @param Request $request
     * @return JsonResponse
     */
    public function setSession(Request $request): JsonResponse
    {
        return new JsonResponse('Session handled successfully');
    }


    /**
     * @Route("/api/isserverup", methods={"GET"}, name="is_server_up")
     * @param Request $request
     * @return JsonResponse
     */
    public function isServerUp(Request $request): JsonResponse
    {
        try {
            $url = $request->query->get('url');

            if ($url === null || filter_var($url, FILTER_VALIDATE_URL) === false) {
                throw new Exception('Invalid URL');
            }

            $headers = @get_headers($url);

            if ($headers && strpos($headers[0], '200')) {
                return new JsonResponse(['status' => 'up']);
            } else {
                return new JsonResponse(['status' => 'down', 'error' => 'Unable to reach the server or server is down']);
            }
        } catch (Exception $e) {
            return new JsonResponse(['status' => 'down', 'error' => $e->getMessage()]);
        }
    }

    /**
     * @Route("/api/session/end/", name="api_end_session")
     * @param Request $request
     * @return JsonResponse
     */
    public function endSession(Request $request): Response
    {
        $response = $this->redirect('/');

        $session = $request->getSession();
        $session->invalidate();

        if ($request->cookies->has(JWTAuthorization::AUTH_COOKIE_NAME)) {
            $this->JWTAuthorization->destroyCookie($request,$response);
        }
        if ($request->cookies->has(JWTAuthorization::REFRESH_TOKEN_COOKIE_NAME)) {
            $this->JWTAuthorization->destroyCookie($request,$response,JWTAuthorization::REFRESH_TOKEN_COOKIE_NAME);
        }
        return $response;
    }

    /**
     * API that checks if a given user's id (uid) is that of a developer.
     * @Route("/api/is-developer-uid", methods={"GET"}, name="api_is_uid_developer")
     * @param Request $request
     */
    public function isDeveloperUid(Request $request): JsonResponse
    {
        if ($request->getMethod() != "GET") {
            return new JsonResponse([
                'status' => 'Method not allowed',
            ], JsonResponse::HTTP_METHOD_NOT_ALLOWED, $headers = [], $json = false);
        }

        $uid = $request->get('uid');

        if (is_null($uid) || empty($uid)) {
            return new JsonResponse([
                'status' => 'Missing argument: uid',
            ], JsonResponse::HTTP_BAD_REQUEST, $headers = [], $json = false);
        }

        $isDeveloper = $this->developerList->isDeveloper($uid);

        return new JsonResponse([
            $isDeveloper,
        ], JsonResponse::HTTP_OK, $headers = [], $json = false);
    }

    /**
     * Route to upload a scanned file to Google Storage. It currently works with one file rather than collection
     * to prevent performance issues.
     * After successful upload, message is pushed to mr_uploaded queue so that the other part of application (in Ruby)
     * knows that there is a file to work on. The Ruby application then manages processing of the file like
     * converting to image, computation of Company name from the image, publishing current status of the process
     * to topic mr_ui_status, ..., publishing the results to mr_predictions. For all this operations we do nothing
     * else then pulling the current status from topic mr_ui_status and results from mr_predictions.
     *
     * @Route("/api/upload-file/", name="api_upload_file")
     * @param Request $request
     * @return JsonResponse
     */
    public function uploadFile(Request $request): JsonResponse
    {
        /** @var UploadedFile[] $files */
        $files = $request->files->all();
        $typeMail = $request->get('typeMail');
        $operator = $request->get('operator');
        $dtc = $request->get('dtc');

        foreach ($files as $file) {
            if (is_array($file)) {
                throw new InvalidArgumentException("Array of files is not implemented");
            }

            if ($file->getClientMimeType() !== "application/pdf") {
                throw new InvalidArgumentException("Should be PDF!");
            }

            $fileName = basename($file->getClientOriginalName());

            $this->storage->uploadFile($file);

            $this->pubSub->pushPdfUploaded($fileName, $typeMail, $operator, $dtc);

            return new JsonResponse($fileName);
        }

        return new JsonResponse("no file send");
    }

    /**
     * @Route("/api/get-batch-count", methods={"GET"}, name="api_get_batch_count")
     * @param Request $request
     */
    public function getBatchCount(Request $request): JsonResponse
    {
        if ($request->getMethod() != "GET") {
            return new JsonResponse([
                'status' => 'Method not allowed',
            ], JsonResponse::HTTP_METHOD_NOT_ALLOWED, $headers = [], $json = false);
        }

        /** @var GlobalMailroomStatusRepository $repository */
        $repository = $this->doctrine->getRepository(GlobalMailroomStatus::class);
        $batchCounter = $repository->getBatchCounterEntity();

        if (
            intval($batchCounter->getDtm()->format('Ymd')) !=
            intval((new DateTime('now', new DateTimeZone('UTC')))->format('Ymd'))
        ) {
            $batchCounter->setValue(0);
        }

        $count = intval($batchCounter->getValue()) + 1;
        $batchCounter->setValue($count);
        $repository->save($batchCounter);

        return new JsonResponse([
            'status' => 'ok',
            'count' => $count
        ], JsonResponse::HTTP_OK);
    }

    /**
     * @Route("/api/acknowledge/", methods={"POST"}, name="api_acknowledge")
     * @param Request $request
     */
    public function acknowledge(Request $request): JsonResponse
    {
        $requestBody = json_decode($request->getContent());

        if (!isset($requestBody->ids)) {
            return new JsonResponse([
                "message" => "Body missing 'ids' property.",
                "status" => "bad_request"
            ], Response::HTTP_BAD_REQUEST);
        }

        $ids = $requestBody->ids;

        if (empty($ids)) {
            return new JsonResponse([
                "message" => "Missing message id(s)",
                "status" => "bad_request"
            ], Response::HTTP_BAD_REQUEST);
        }

        /**
         * @var StatusMessageRepository $statusMessageRepository
         */
        $statusMessageRepository = $this->doctrine->getRepository(StatusMessage::class);

        foreach ($ids as $id) {
            $results[$id] = $statusMessageRepository->removeById($id);
        }

        return new JsonResponse([
            'result' => $results,
            'status' => 'ok',
        ], Response::HTTP_OK);
    }

    /**
     * @Route("api/acknowledge-file/{ackId?null}", methods={"DELETE"}, name="api_acknowledge_file")
     * @param Request $request
     * @return JsonResponse
     */
    public function acknowledgeFile(Request $request): JsonResponse
    {
        if (empty($ackId = $request->attributes->get('ackId'))) {
            return new JsonResponse([
                "message" => "You need to inform the message ID",
                "status" => "bad_request"
            ], Response::HTTP_BAD_REQUEST);
        }

        $ackIdnowledge = new Message([], ['ackId' => $ackId]);
        $acknowledge = false;
        $error = null;

        // Iterate over the list of topics names and check in which of them
        // the message are inserted
        foreach (Topic::TOPICS as $topic) {
            $topicCheck = Topic::fromString($topic);
            try {
                $this->pubSub->acknowledge($topicCheck, $ackIdnowledge);
                $acknowledge = true;
                break;
            } catch (Exception $e) {
                $error = $e;
            }
        }

        if (!$acknowledge) {
            return new JsonResponse($error->getMessage(), 500);
        }

        return new JsonResponse("OK", 200);
    }

    /**
     * Once admin resolves the item (confirms the company is matched correctly by Google AI,
     * or finds the matching company manually) he/she 'resolves' the item, which means the post request
     * is made to this route with data
     *  with the post item info and matching company info.
     * In this route, the result is then push to Google PubSub mr_result topic, message from mr_prediction is acknowledged,
     * and the job for the particular item is done by this part of application. (The rest is responsibility of the Ruby
     * application which will store the result in the CMS database).
     *
     * @Route("/api/result/", methods={"POST"}, name="api_result")
     * @param Result $result // ResultParamConverter is applied
     * @return JsonResponse
     */
    public function result(Result $result): JsonResponse
    {
        try {
            $this->pubSub->pushToMrCompleted(['message' => $result->getCompleted(), 'ackId' => $result->getAckId()]);
//            $this->pubSub->acknowledge(Topic::waitingForResolution(), $result->getMessage());
            return new JsonResponse([
                'message' => "Acknowledged",
                'ack_id' => $result->getAckId()
            ]);
        } catch (Exception $e) {
            return new JsonResponse([
                'message' => "Failed to push to mr completed or acknowledge item waiting for resolution",
                'ack_id' => $result->getAckId(),
                'debug' => [
                    'at' => $e->getFile(),
                    'line' => $e->getLine(),
                    'trace' => $e->getTraceAsString(),
                    'exception' => $e->getMessage()
                ]
            ], 500);
        }
    }

    /**
     * The Ruby application pushes every state of the post item to the Google PubSub topic mr_ui_status.
     * In this route, we are pulling the messages from this topic to show the status to the admin.
     *
     * @Route("/api/pull-status/", name="api_pull_status")
     * @param Request $request
     * @return JsonResponse
     */
    public function pullStatus(Request $request): JsonResponse
    {
        try {
            if (empty($uid = $request->get('uid'))) {
                return new JsonResponse([
                    'result' => 'Missing `uid` parameter.'
                ], Response::HTTP_BAD_REQUEST);
            }

            if (empty($batchIds = explode(',', $request->get('batch_ids')))) {
                return new JsonResponse([
                    'result' => 'Missing `batch_id` parameter.'
                ], Response::HTTP_BAD_REQUEST);
            }

            /**
             * @var StatusMessageRepository $statusMessageRepository
             */
            $statusMessageRepository = $this->doctrine->getRepository(StatusMessage::class);

            $this->updateStatusMessageDatabase($uid, $batchIds, $statusMessageRepository);

            $userMessages = $this->formatMessages($statusMessageRepository->findByUid($uid));

            if (count($userMessages) > 0) {
                return new JsonResponse([
                    'result' => 'new messages for user found',
                    'messages' => $userMessages
                ], Response::HTTP_OK);
            }

            return new JsonResponse([
                'result' => 'No messages'
            ], Response::HTTP_OK);
        } catch(Exception $e) {
            return new JsonResponse([
                'result' => 'Unexpected error',
                'messages' => [],
                'debug' => [
                    'at' => $e->getFile(),
                    'line' => $e->getLine(),
                    'trace' => $e->getTraceAsString(),
                    'exception' => $e->getMessage()
                ]
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

    }

    /**
     * @Route("/api/pull-waiting/", name="api_pull_waiting")
     */
    public function pullWaiting(): JsonResponse
    {
        #Change the message from mr_predictions to mr_waiting_for_resolution
        $this->pubSub->pullMrPredictions();
        $messages = $this->pubSub->pullWaiting();

        return new JsonResponse(
            array_map(
                function (Message $message) {
                    $data = json_decode($message->data(), true);
                    if (!empty($this->storage->getFile($data['pdf_filename'])->exists())) {
                        return array_merge(
                            $data,
                            ["message" => serialize($message)],
                            ["ack_id" => $message->ackId()],
                            ["file_exists" => $this->storage->getFile($data['pdf_filename'])->exists()]
                        );
                    }
                },
                $messages
            )
        );
    }

    /**
     * The Ruby application pushes the computed predictions (data with suggested match of company and file) to
     * Google PubSub topic mr_predictions.
     * In this route, we are pulling the messages from mr_predictions to show the suggested matches to the admin
     * who will further process them (either confirms to correctness of some prediction or proceeds to manual company search).
     *
     * @Route("/api/pull-mr-predictions/", name="api_pull_mr_predictions")
     */
    public function mrPredictions()
    {
        $this->pubSub->pullMrPredictions();
        return new JsonResponse('ok');
    }

    /**
     * It accepts data with company name or officer name or lp number and passes the request further to CMS endpoint
     * where the search in CMS and VO databases actually happens and result is then return back to the admin for further
     * processing. In the result there are more rows of possible matches - admin is supposed yet to pick to correct one.
     * todo: there could be too many rows found - some kind of pagination might be needed in here, or on CMS endpoint side.
     *
     * @Route("/api/company-search/", methods={"GET"}, name="api_company_search")
     */
    public function companySearch(Request $request): JsonResponse
    {
        try {
            $response = $this->companiesMadeSimple->companySearch($request->getQueryString());

            return new JsonResponse(json_decode($response->getContent()));
        } catch (\Throwable $e) {
            return new JsonResponse($e->getMessage(), JsonResponse::HTTP_INTERNAL_SERVER_ERROR, $e->getTrace());
        }
    }

    /**
     * It accepts data with company name or officer name or lp number and passes the request further to CMS endpoint
     * where the search in CMS and VO databases actually happens and result is then return back to the admin for further
     * processing. In the result there are more rows of possible matches - admin is supposed yet to pick to correct one.
     * todo: there could be too many rows found - some kind of pagination might be needed in here, or on CMS endpoint side.
     *
     * @Route("/api/old-company-search/", methods={"GET"}, name="api_old_company_search")
     */
    public function oldCompanySearch(Request $request): JsonResponse
    {
        try {
            $response = $this->companiesMadeSimple->oldCompanySearch($request->getQueryString());

            $responseArray = json_decode($response->getContent(), true);
            $formattedResponseArray = [];
            foreach ($responseArray as $company) {
                // Skip MSG
                if ($company['id'] === 'LP51505') {
                    continue;
                }

                if (empty($company['services'])) {
                    $formattedResponseArray[] = [
                        'id'                => $company['id'],
                        'companyName'       => $company['name'],
                        'companyNumber'     => $company['number'],
                        'serviceName'       => null,
                        'serviceBeginning'  => null,
                        'serviceExpiration' => null,
                        'members'           => null,
                    ];
                    continue;
                }

                $formattedResponseArray[] = [
                    'id'                => $company['id'],
                    'companyName'       => $company['name'],
                    'companyNumber'     => $company['number'],
                    'serviceName'       => $company['site'] === 'CMS' ? reset($company['services'])[1] : 'VO',
                    'serviceBeginning'  => reset($company['services'])[5] === 'ACTIVE' ? (new DateTime('-1 day'))->format('Y-m-d') : null,
                    'serviceExpiration' => reset($company['services'])[5] === 'ACTIVE' ? (new DateTime('+1 day'))->format('Y-m-d') : null,
                    'members'           => null,
                ];
            }

            return new JsonResponse($formattedResponseArray);
        } catch (\Throwable $e) {
            return new JsonResponse([$e->getMessage(), $e->getTrace()], JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * API that takes in a GET request with a company number as a parameter and returns all post items for that company.
     *
     * @Route("/api/get-post-items", methods={"GET", "POST"}, name="get_company_post_items")
     */
    public function getCompanyPostItems(Request $request, ManagerRegistry $doctrine): JsonResponse
    {
        if ($request->getMethod() != "GET" && $request->getMethod() != "POST") {
            return new JsonResponse(
                [
                    'status' => 'method_not_allowed',
                ],
                JsonResponse::HTTP_METHOD_NOT_ALLOWED,
                $headers = [],
                $json = false
            );
        }

        if ($request->getMethod() == "GET") {
            $cnum = $request->get('cnum');
            $pnum = $request->get('pnum');
            $psize = $request->get('psize');

            $companyNumbers = $cnum ? explode(',', $cnum) : null;
            $pageNumber = $pnum ? intval($pnum) : null;
            $pageSize = $psize ? intval($psize) : null;
        }

        if ($request->getMethod() == "POST") {
            $body = json_decode($request->getContent(), true);
            $companyNumbers = $body['company_numbers'] ?? null;
            $pageNumber = $body['page_number'] ?? null;
            $pageSize = $body['page_size'] ?? null;
        }

        /** @var PostItemRepository $repository*/
        $repository = $doctrine->getRepository(PostItem::class);

        $totalItems = $repository->count(criteria: [
            'companyNumbers' => $companyNumbers,
            'status' => PostItem::COMPANY_INBOX_STATUSES,
        ]);

        $postItemList = $repository->findByCompanyNumbers(criteria: [
            'includedStatuses' => PostItem::COMPANY_INBOX_STATUSES,
            'companyNumbers' => $companyNumbers,
            'pageSize' => $pageSize,
            'pageNumber' => $pageNumber,
        ]);

        if (empty($postItemList)) {
            return new JsonResponse(
                [
                    'status' => 'post_items_for_company_not_found',
                    'total_items' => 0,
                    'data' => []
                ],
                JsonResponse::HTTP_OK,
                $headers = [],
                $json = false
            );
        }

        return new JsonResponse(
            [
                'status' => 'post_items_successfully_listed',
                'total_items' => $totalItems,
                'data' => $this->buildApiPostItemListData($postItemList)
            ],
            JsonResponse::HTTP_OK,
            $headers = [],
            $json = false
        );
    }

    /**
     * API that takes in a GET request with a post_item_id as a parameter
     * and returns the corresponding post item.
     *
     * @Route("/api/get-post-item", methods={"GET"}, name="get_post_item")
     */
    public function getPostItem(Request $request, ManagerRegistry $doctrine): JsonResponse
    {
        if ($request->getMethod() != "GET") {
            return new JsonResponse(
                [
                    'status' => 'method_not_allowed',
                ],
                JsonResponse::HTTP_METHOD_NOT_ALLOWED,
                [],
                false
            );
        }

        $postItemId = $request->get('post_item_id');

        if (!$postItemId) {
            return new JsonResponse(
                [
                    'status' => 'post_item_id_not_provided',
                ],
                JsonResponse::HTTP_UNPROCESSABLE_ENTITY,
                [],
                false
            );
        }

        $repository = $doctrine->getRepository(PostItem::class);

        try {
            $postItem = $repository->findOneBy(['id' => $postItemId]);
        } catch (ConversionException $e) {
            return new JsonResponse(
                [
                    'error' => $e->getMessage(),
                ],
                JsonResponse::HTTP_NOT_FOUND,
            );
        }

        if (empty($postItem)) {
            return new JsonResponse(
                [
                    'status' => "post_item_not_found",
                    'data' => []
                ],
                JsonResponse::HTTP_OK,
                [],
                false
            );
        }

        return new JsonResponse([
            'status' => "post_item_successfully_retrieved",
            'data' => $this->buildApiPostItemData($postItem),
        ], JsonResponse::HTTP_OK, [], false);
    }

    /**
     * API that takes in a GET request with a post_item_id and region as parameters
     * and returns the corresponding parcel transit fee.
     *
     * @Route("/api/get-parcel-transit-fee", methods={"GET"}, name="get_parcel_transit_fee")
     */
    public function getParcelTransitFee(Request $request): JsonResponse
    {
        $postItemId = $request->get('post_item_id');
        $region = $request->get('region');

        if (!$postItemId || !$region) {
            return new JsonResponse(
                [
                    'status' => 'Missing parameters: ' . (!$postItemId ? 'post_item_id' : '') . (!$region ? 'country' : ''),
                ],
                JsonResponse::HTTP_UNPROCESSABLE_ENTITY,
                [],
                false
            );
        }

        if (!in_array($region, TransitFee::REGIONS)) {
            return new JsonResponse(
                [
                    'status' => 'Invalid region provided',
                ],
                JsonResponse::HTTP_UNPROCESSABLE_ENTITY,
                [],
                false
            );
        }

        $postItemRepository = $this->doctrine->getRepository(PostItem::class);

        $postItem = $postItemRepository->findOneBy(['id' => $postItemId]);

        if (empty($postItem)) {
            return new JsonResponse(
                [
                    'status' => "post item not found",
                    'data' => []
                ],
                JsonResponse::HTTP_OK,
                [],
                false
            );
        }

        if ($postItem->getType() != PostItem::TYPE_PARCEL) {
            return new JsonResponse(
                [
                    'status' => "post item is not a parcel",
                ],
                JsonResponse::HTTP_UNPROCESSABLE_ENTITY,
                [],
                false
            );
        }

        if (empty($postItemWeight = intval($postItem->getDetailByKey('total_weight')->getValue()))) {
            return new JsonResponse(
                [
                    'status' => "post item weight not found",
                ],
                JsonResponse::HTTP_UNPROCESSABLE_ENTITY,
                [],
                false
            );
        }

        $weightBracket = null;
        foreach (TransitFee::FEE_BRACKETS as $bracket) {
            $min = intval(explode('_', $bracket)[0]);
            $max = intval(explode('_', $bracket)[1]);

            if ($postItemWeight >= $min && $postItemWeight <= $max) {
                $weightBracket = $bracket;
                break;
            }
        }

        if (empty($weightBracket)) {
            return new JsonResponse(
                [
                    'status' => "post item weight not within any bracket",
                ],
                JsonResponse::HTTP_UNPROCESSABLE_ENTITY,
                [],
                false
            );
        }

        $transitFeeRepository = $this->doctrine->getRepository(TransitFee::class);
        $transitFee = $transitFeeRepository->findOneBy(['feeName' => $weightBracket]);
        $transitFee = match ($region) {
            TransitFee::REGION_UK     => $transitFee->getUkFee(),
            TransitFee::REGION_EUROPE => $transitFee->getEuropeFee(),
            TransitFee::REGION_WORLD  => $transitFee->getWorldFee(),
        };

        return new JsonResponse([
            'status' => "post item transit fee successfully retrieved",
            'data' => [
                'transit_fee' => $transitFee,
            ],
        ], JsonResponse::HTTP_OK, [], false);
    }

    /**
     * API that returns all post items that have already been forwarded and don't yet have an `order_id` detail set
     * (not yet charged).
     *
     * @Route("/api/get-unpaid-forwarded-items", methods={"GET"}, name="get_unpaid_forwarded_items")
     */
    public function getUnpaidForwardedItems(Request $request, ManagerRegistry $doctrine): JsonResponse
    {
        if ($request->getMethod() != 'GET') {
            return new JsonResponse(
                [
                    'status' => 'method_not_allowed',
                ],
                JsonResponse::HTTP_METHOD_NOT_ALLOWED,
                $headers = [],
                $json = false
            );
        }

        /**
         * @var PostItemRepository $repository
         */
        $repository = $doctrine->getRepository(PostItem::class);

        $postItemList = $repository->getUnpaidForwardedItems();

        if (empty($postItemList)) {
            return new JsonResponse(
                [
                    'status' => 'no_forwarded_post_items_to_charge',
                    'data' => []
                ],
                JsonResponse::HTTP_OK,
                $headers = [],
                $json = false
            );
        }

        return new JsonResponse(
            [
                'status' => 'post_items_successfully_listed',
                'data' => $this->buildApiPostItemListData($postItemList),
            ],
            JsonResponse::HTTP_OK,
            $headers = [],
            $json = false
        );
    }


    /**
     * API that returns all post items that have charging attempts that haven't been released yet.
     *
     * @Route("/api/get-failed-payment-post-items", methods={"POST"}, name="failed_payment_post_items")
     */
    public function getFailedPaymentPostItems(Request $request, ManagerRegistry $doctrine): JsonResponse
    {
        if ($request->getMethod() != 'POST') {
            return new JsonResponse(
                [
                    'status' => 'method_not_allowed',
                ],
                JsonResponse::HTTP_METHOD_NOT_ALLOWED,
                $headers = [],
                $json = false
            );
        }

        $body = json_decode($request->getContent(), true);
        $companyNumbersArray = $body['company_numbers'] ?? [];

        if (empty($companyNumbersArray)) {
            return new JsonResponse(
                [
                    'status' => 'no_company_numbers_provided',
                ],
                JsonResponse::HTTP_BAD_REQUEST
            );
        }

        if (!is_null($companyNumbersArray) && !is_array($companyNumbersArray)) {
            return new JsonResponse(
                [
                    'status' => 'invalid_parameter',
                    'message' => 'cnums must be an array if provided.',
                ],
                JsonResponse::HTTP_BAD_REQUEST
            );
        }

        /** @var PostItemRepository $repository **/
        $repository = $doctrine->getRepository(PostItem::class);
        $postItemList = $repository->getFailedPaymentItems($companyNumbersArray);

        if (empty($postItemList)) {
            return new JsonResponse(
                [
                    'status' => 'no_post_items_with_failed_payments',
                    'data' => []
                ],
                JsonResponse::HTTP_OK,
                $headers = [],
                $json = false
            );
        }

        return new JsonResponse(
            [
                'status' => 'post_items_successfully_listed',
                'data' => $this->buildApiPostItemListData($postItemList),
            ],
            JsonResponse::HTTP_OK,
            $headers = [],
            $json = false
        );
    }

    /**
     * API that takes in a PUT request with a `post_items` array where the keys are post item IDs and the values are the
     * order IDs. This API will mark the items as purchased by setting the order IDs as a details to the post items.
     *
     * @Route("/api/mark-item-as-purchased", methods={"PUT"}, name="mark_item_as_purchased")
     */
    public function markItemAsPurchased(Request $request): JsonResponse
    {
        $body = json_decode($request->getContent(), true);

        if ($request->getMethod() != "PUT") {
            return new JsonResponse([
                'status' => 'Method not allowed'
            ], JsonResponse::HTTP_METHOD_NOT_ALLOWED, $headers = [], $json = false);
        }

        if (empty($body)) {
            return new JsonResponse([
                'status' => 'Bad request'
            ], JsonResponse::HTTP_BAD_REQUEST, $headers = [], $json = false);
        }

        if (!$postItemId = $body['post_item_id']) {
            return new JsonResponse([
                'status' => 'Post Item not provided'
            ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY, $headers = [], $json = false);
        }

        if (!$transactionId = $body['transaction_id']) {
            return new JsonResponse([
                'status' => 'Transaction ID not provided'
            ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY, $headers = [], $json = false);
        }

        if (!$desiredStatus = $body['desired_status']) {
            return new JsonResponse([
                'status' => 'Desired status not provided'
            ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY, $headers = [], $json = false);
        }

        if (!in_array($desiredStatus, array_keys(PostItem::PARSED_STATUSES))) {
            return new JsonResponse([
                'status' => 'Invalid desired status'
            ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY, $headers = [], $json = false);
        }

        /** @var PostItemRepository $repository */
        $repository = $this->doctrine->getRepository(PostItem::class);

        $postItem = $repository->findOneById($postItemId);

        if (!is_null($postItem->getTransactionId())) {
            return new JsonResponse(
                [
                    'status' => 'Post item already marked as purchased',
                ],
                JsonResponse::HTTP_UNPROCESSABLE_ENTITY,
                $headers = [],
                $json = false
            );
        }

        $postItem->setTransactionId($transactionId);
        $postItem->setStatus($desiredStatus);

        $eventLog = new PostItemEventLog($this->doctrine->getManager(), $postItem);
        $eventLog->save(
            'Post item paid for',
            'CMS'
        );

        if ($eventLog->hasAnyError()) {
            return new JsonResponse(
                [
                    'status' => 'Error when saving event log.',
                    'errors' => $eventLog->getErrors()
                ],
                JsonResponse::HTTP_INTERNAL_SERVER_ERROR,
                $headers = [],
                $json = false
            );
        }

        $this->doctrine->getManager()->persist($postItem);
        $this->doctrine->getManager()->flush();

        return new JsonResponse(
            [
                'status' => 'Successfully marked as purchased',
            ],
            JsonResponse::HTTP_OK,
            $headers = [],
            $json = false
        );
    }

    /**
     * API that takes in a PUT request with a `post_items` array where the keys are post item IDs and the values are the
     * order IDs. This API will mark the items as charged by setting the order IDs as a details to the post items.
     *
     * @Route("/api/mark-items-as-charged", methods={"PUT"}, name="mark_item_as_charged")
     */
    public function markItemsAsCharged(Request $request, ManagerRegistry $doctrine): JsonResponse
    {
        $body = json_decode($request->getContent(), true);

        if ($request->getMethod() != "PUT") {
            return new JsonResponse([
                'status' => 'Method not allowed'
            ], JsonResponse::HTTP_METHOD_NOT_ALLOWED, $headers = [], $json = false);
        }

        if (empty($body)) {
            return new JsonResponse([
                'status' => 'Bad request'
            ], JsonResponse::HTTP_BAD_REQUEST, $headers = [], $json = false);
        }

        if (!$postItems = $body['post_items']) {
            return new JsonResponse([
                'status' => 'Post Items not provided'
            ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY, $headers = [], $json = false);
        }

        $results = $this->setPostItemOrderIds($postItems, $doctrine);
        $succeededAmount = $this->countSuccessfulResults($results);
        $status = $this->generateStatusMessageForOrderIdOperation($succeededAmount, count($results));

        return new JsonResponse([
            'status' => $status,
            'results' => $results
        ], JsonResponse::HTTP_OK, $headers = [], $json = false);
    }

    /**
     * API that takes in a PUT request with a post item ID and an email ID and sets the last email sent for the post item.
     *
     * @Route("/api/set-last-email-sent", methods={"PUT"}, name="set_last_email_sent")
     */
    public function setLastEmailSent(Request $request, ManagerRegistry $doctrine): JsonResponse
    {
        $body = json_decode($request->getContent(), true);

        if ($request->getMethod() != "PUT") {
            return new JsonResponse([
                'status' => 'Method not allowed',
                'result' => 0
            ], JsonResponse::HTTP_METHOD_NOT_ALLOWED, $headers = [], $json = false);
        }

        if (empty($body)) {
            return new JsonResponse([
                'status' => 'Bad request',
                'result' => 0
            ], JsonResponse::HTTP_BAD_REQUEST, $headers = [], $json = false);
        }

        if (!$postItems = $body['post_items']) {
            return new JsonResponse([
                'status' => 'No post items provided',
                'result' => 0
            ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY, $headers = [], $json = false);
        }

        foreach ($postItems as $postItem) {
            if (!isset($postItem['post_item_id'])) continue;
            if (!isset($postItem['email_log_id'])) continue;
            if (!isset($postItem['email_name'])) continue;

            $result = $this->setPostItemLastEmailSent($postItem['post_item_id'], $postItem['email_name'], $doctrine);
            $result['event'] = $this->logEmailSentEvent(
                $postItem['post_item_id'],
                $postItem['email_name'],
                $postItem['email_log_id'],
                $doctrine
            );
        }

        if (empty($result) || !isset($result['status'])) {
            return new JsonResponse([
                'status' => 'Post Item not found',
                'result' => 0
            ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY, $headers = [], $json = false);
        }

        return new JsonResponse($result, JsonResponse::HTTP_OK, $headers = [], $json = false);
    }

    /**
     * API that takes in a GET request all post items not released.
     *
     * @Route("/api/get-waiting-non-statutory-post-items", methods={"GET"}, name="get_waiting_non_statutory_post_items")
     */
    public function getWaitingNonStatutoryPostItems(Request $request, ManagerRegistry $doctrine): JsonResponse
    {
        $companyNumber = $request->get('cnum');

        if ($request->getMethod() != "GET") {
            return new JsonResponse(
                [
                    'status' => 'method_not_allowed',
                ],
                JsonResponse::HTTP_METHOD_NOT_ALLOWED,
                $headers = [],
                $json = false
            );
        }

        /**
         * @var PostItemRepository $repository
         */
        $repository = $doctrine->getRepository(PostItem::class);
        if ($companyNumber) {
            $postItemList = $repository->getWaitingNonStatutoryByCompany($companyNumber);
        } else {
            $postItemList = $repository->getWaitingNonStatutory();
        }

        if (empty($postItemList)) {
            return new JsonResponse(
                [
                    'status' => "no_post_items",
                    'data' => []
                ],
                JsonResponse::HTTP_OK,
                $headers = [],
                $json = false
            );
        }

        return new JsonResponse(
            [
                'status' => 'post_items_successfully_listed',
                'data' => $this->buildApiPostItemListData($postItemList),
            ],
            JsonResponse::HTTP_OK,
            $headers = [],
            $json = false
        );
    }

    /**
     * API that takes in a GET request all post items not released.
     *
     * @Route("/api/get-unprocessed-post-items", methods={"GET"}, name="get_unprocessed_post_items")
     * @throws Exception
     */
    public function getUnprocessedPostItems(Request $request, ManagerRegistry $doctrine): JsonResponse
    {
        if ($request->getMethod() != "GET") {
            return new JsonResponse(
                [
                    'status' => 'method_not_allowed',
                ],
                JsonResponse::HTTP_METHOD_NOT_ALLOWED,
                $headers = [],
                $json = false
            );
        }

        $postItemList = $this->getPostItemsFromStatusList(
            $doctrine,
            PostItem::PROCESSING_SCRIPT_STATUSES,
            $request->get('limit'),
            $request->get('lastId')
        );

        if (empty($postItemList)) {
            return new JsonResponse(
                [
                    'status' => "no_post_items",
                    'data' => []
                ],
                JsonResponse::HTTP_OK,
                $headers = [],
                $json = false
            );
        }

        return new JsonResponse(
            [
                'status' => 'post_items_successfully_listed',
                'data' => $this->buildApiPostItemListData($postItemList),
            ],
            JsonResponse::HTTP_OK,
            $headers = [],
            $json = false
        );
    }

    /**
     * API that takes in a GET request all processed post items that could require an update.
     *
     * @Route("/api/get-require-update-post-items", methods={"GET"}, name="get_require_update_post_items")
     */
    public function getRequireUpdatePostItems(Request $request, ManagerRegistry $doctrine): JsonResponse
    {
        if ($request->getMethod() != "GET") {
            return new JsonResponse(
                [
                    'status' => 'method_not_allowed',
                ],
                JsonResponse::HTTP_METHOD_NOT_ALLOWED,
                $headers = [],
                $json = false
            );
        }

        $postItemList = $this->getPostItemsFromStatusList(
            $doctrine,
            PostItem::UPDATE_SCRIPT_STATUSES,
            $request->get('limit', null),
            $request->get('lastId', null)
        );

        if (empty($postItemList)) {
            return new JsonResponse(
                [
                    'status' => "no_post_items",
                    'data' => []
                ],
                JsonResponse::HTTP_OK,
                $headers = [],
                $json = false
            );
        }

        return new JsonResponse(
            [
                'status' => 'post_items_successfully_listed',
                'data' => $this->buildApiPostItemListData($postItemList),
            ],
            JsonResponse::HTTP_OK,
            $headers = [],
            $json = false
        );
    }

    /**
     * API that takes in a GET request all post items already released.
     *
     * @Route("/api/get-released-non-statutory-post-items", methods={"GET"}, name="get_released_non_statutory_post_items")
     */
    public function getReleasedNonStatutoryPostItems(Request $request, ManagerRegistry $doctrine): JsonResponse
    {
        $companyNumber = $request->get('cnum');

        if ($request->getMethod() != "GET") {
            return new JsonResponse(
                [
                    'status' => 'method_not_allowed',
                ],
                JsonResponse::HTTP_METHOD_NOT_ALLOWED,
                $headers = [],
                $json = false
            );
        }

        /**
         * @var PostItemRepository $repository
         */
        $repository = $doctrine->getRepository(PostItem::class);
        if ($companyNumber) {
            $postItemList = $repository->getReleasedNonStatutoryByCompany($companyNumber);
        } else {
            $postItemList = $repository->getReleasedNonStatutory();
        }

        if (empty($postItemList)) {
            return new JsonResponse(
                [
                    'status' => "no_post_items",
                    'data' => []
                ],
                JsonResponse::HTTP_OK,
                $headers = [],
                $json = false
            );
        }

        return new JsonResponse(
            [
                'status' => 'post_items_successfully_listed',
                'data' => $this->buildApiPostItemListData($postItemList),
            ],
            JsonResponse::HTTP_OK,
            $headers = [],
            $json = false
        );
    }

    /**
     * API that adds a post item to the mailroom database.
     *
     * @Route("/api/add-post-item", methods={"POST"}, name="add_post_item")
     * @Route("/api/add-post-item-external", methods={"POST"}, name="add_post_item_external")
     */
    public function addPostItem(Request $request, ManagerRegistry $doctrine): JsonResponse
    {
        $body = json_decode($request->getContent(), true);

        if ($request->getMethod() != "POST"){
            return new JsonResponse([
                'status' => 'Method not allowed',
            ], JsonResponse::HTTP_METHOD_NOT_ALLOWED, $headers = [], $json = false);
        }

        if (empty($body)){
            return new JsonResponse([
                'status' => 'Bad request',
            ], JsonResponse::HTTP_BAD_REQUEST, $headers = [], $json = false);
        }

        if (
            !isset($body['type_mail']) ||
            !isset($body['companyNumber']) ||
            !isset($body['companyName']) ||
            !isset($body['batchId']) ||
            !isset($body['pdf_filename'])
        ){
            if (empty($body)){
                return new JsonResponse([
                    'status' => 'Bad request',
                ], JsonResponse::HTTP_BAD_REQUEST, $headers = [], $json = false);
            }
        }

        if (!isset($body['sender'])) {
            $type = 'other';
            $sender = 'OTHER';
            switch ($body['type_mail']) {
                case 'HMRC':
                case 'COMPANIES_HOUSE':
                case 'OTHER':
                    $type = 'statutory';
                    $sender = $body['type_mail'];
                    break;

                case 'NON_STATUTORY':
                    $type = 'non-statutory';
                    break;
            }
        } else {
            $type = $body['type_mail'];
            $sender = $body['sender'];
        }

        $operator = $body['operator'] ?? $this->JWTAuthorization->getUser($request);

        // This is a tool for testing sentry
        $fileNameNoBatch = explode('_', $body['pdf_filename']);
        if (end($fileNameNoBatch) === 'test.pdf') {
            $this->logger->critical('testing sentry, hello!');
        }
        // ---------------------------------

        /** @var PostItemRepository $postItemRepository */
        $postItemRepository = $doctrine->getRepository(PostItem::class);

        if ($postItemRepository->findOneBy(['fileName' => $body['pdf_filename']])) {
            $this->logger->critical(sprintf('Item already exists: %s', $body['pdf_filename']));

            return new JsonResponse([
                'status' => 'Item already exists',
            ], JsonResponse::HTTP_CONFLICT, $headers = [], $json = false);
        }

        $postItem = new AddPostItem(
            objectManager: $this->doctrine->getManager(),
            operator: $operator,
            companyNumber: $body['companyNumber'],
            companyName: $body['companyName'],
            type: $type,
            sender: $sender,
            batchName: $body['batchId'],
            fileName: $body['pdf_filename'],
            filePath: $this->getParameter('storage_url'),
            scanned: !!$request->get('scanned'),
            status: $body['sender'] === 'COURT_LETTER' ? PostItem::STATUS_SCAN_ONLY : PostItem::STATUS_ADDED,
        );

        $savedPostItem = $postItem->save();

        if ($postItem->hasAnyError()) {
            return new JsonResponse([
                'status' => 'Internal server error',
                'data' => $postItem->getErrors()
            ], JsonResponse::HTTP_INTERNAL_SERVER_ERROR, $headers = [], $json = false);
        }

        if ($savedPostItem && !empty($body['formData'])) {
            $data = json_decode($body['formData'], true);
            $data['id'] = $savedPostItem->getId();
            $this->pubSub->pushToMrCompleted($data);
        }

        if ($savedPostItem && $body['sender'] === 'COURT_LETTER') {
            $this->companiesMadeSimple->sendCourtLetterEmail($savedPostItem->getCompanyNumber());
        }

        if ($savedPostItem && !empty($body['events'])) {
            foreach ($body['events'] as $event) {
                $eventLog = new PostItemEventLog($this->doctrine->getManager(), $savedPostItem);
                $eventLog->save(
                    $event['event_name'],
                    $operator
                );
            }
        }

        if ($savedPostItem && !empty($body['details'])) {
            foreach ($body['details'] as $detailKey => $detailValue) {
                $postItemDetail = new PostItemDetail($this->doctrine->getManager(), $savedPostItem);
                $postItemDetail->save(
                    $detailKey,
                    $detailValue
                );
            }
        }

        return new JsonResponse([
            'status' => 'Item created successfully',
            'post_item_id' => $savedPostItem->getId(),
        ], JsonResponse::HTTP_CREATED, $headers = [], $json = false);
    }

    /**
     * API that adds a custom event to a post item.
     *
     * @Route("/api/add-post-item-event", methods={"POST"}, name="add_post_item_event")
     */
    public function addPostItemEvent(Request $request): JsonResponse
    {
        if ($request->getMethod() != "POST") {
            return new JsonResponse([
                'status' => 'Method not allowed',
            ], JsonResponse::HTTP_METHOD_NOT_ALLOWED, $headers = [], $json = false);
        }

        $body = json_decode($request->getContent(), true);

        if (empty($body)) {
            return new JsonResponse([
                'status' => 'Bad request',
            ], JsonResponse::HTTP_BAD_REQUEST, $headers = [], $json = false);
        }

        foreach ($body as $event) {
            if (
                !isset($event['post_item_id']) ||
                !isset($event['event_name'])
            ) {
                if (empty($event)) {
                    return new JsonResponse([
                        'status' => 'Bad request',
                    ], JsonResponse::HTTP_BAD_REQUEST, $headers = [], $json = false);
                }
            }

            /** @var PostItemRepository $itemRepository */
            $itemRepository = $this->doctrine->getRepository(PostItem::class);

            $postItem = $itemRepository->findOneBy(['id' => $event['post_item_id']]);

            if (empty($postItem)) {
                $result[$event['post_item_id']] = 0;
                continue;
            }

            if (isset($event['status_to_set'])) {
                $postItem->setStatus($event['status_to_set']);
                $this->doctrine->getManager()->persist($postItem);
                $this->doctrine->getManager()->flush();
            }

            if (isset($event['details_to_add'])) {
                foreach ($event['details_to_add'] as $detailKey => $detailValue) {
                    $postItemDetail = new PostItemDetail($this->doctrine->getManager(), $postItem);
                    $postItemDetail->save(
                        $detailKey,
                        $detailValue
                    );
                }
            }

            $eventLog = new PostItemEventLog($this->doctrine->getManager(), $postItem);
            $eventLog->save(
                $event['event_name'],
                'CMS',
            );

            if ($eventLog->hasAnyError()) {
                $result[$event['post_item_id']] = 0;
                continue;
            }

            $result[$event['post_item_id']] = 1;
        }

        $successfulResults = count(array_filter($result, function ($value) {
            return $value === 1;
        }));

        if (count($result) === $successfulResults) {
            return new JsonResponse([
                'status' => 'Post Item status updated'
            ], JsonResponse::HTTP_OK, $headers = [], $json = false);
        }

        if ($successfulResults === 0) {
            return new JsonResponse([
                'status' => 'Post items not updated',
                'data'   => $result
            ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY, $headers = [], $json = false);
        }

        return new JsonResponse([
            'status' => 'Some post items not updated',
            'data'   => $result
        ], JsonResponse::HTTP_PARTIAL_CONTENT, $headers = [], $json = false);
    }

    /**
     * API that adds a custom detail to a post item.
     *
     * @Route("/api/add-post-item-detail", methods={"POST"}, name="add_post_item_detail")
     */
    public function addPostItemDetail(Request $request): JsonResponse
    {
        if ($request->getMethod() != "POST") {
            return new JsonResponse([
                'status' => 'Method not allowed',
            ], JsonResponse::HTTP_METHOD_NOT_ALLOWED, $headers = [], $json = false);
        }

        $body = json_decode($request->getContent(), true);

        if (empty($body)) {
            return new JsonResponse([
                'status' => 'Bad request',
            ], JsonResponse::HTTP_BAD_REQUEST, $headers = [], $json = false);
        }

        /** @var PostItemRepository $itemRepository */
        $itemRepository = $this->doctrine->getRepository(PostItem::class);

        $result = [];
        foreach ($body as $detail) {
            if (
                !isset($detail['post_item_id']) ||
                !isset($detail['detail_key']) ||
                !isset($detail['detail_value'])
            ) {
                return new JsonResponse([
                    'status' => 'Bad request',
                ], JsonResponse::HTTP_BAD_REQUEST, $headers = [], $json = false);
            }

            $postItem = $itemRepository->findOneBy(['id' => $detail['post_item_id']]);

            if (empty($postItem)) {
                $result[$detail['post_item_id']] = 0;
                continue;
            }

            $postItemDetail = new PostItemDetail($this->doctrine->getManager(), $postItem);

            $postItemDetail->save(
                key: $detail['detail_key'],
                value: $detail['detail_value']
            );

            if ($postItemDetail->hasAnyError()) {
                $result[$detail['post_item_id']] = 0;
                continue;
            }

            $result[$detail['post_item_id']] = 1;
        }

        $successfulResults = count(array_filter($result, function ($value) {
            return $value === 1;
        }));

        if (count($result) === $successfulResults) {
            return new JsonResponse([
                'status' => 'Post Item status updated'
            ], JsonResponse::HTTP_OK, $headers = [], $json = false);
        }

        if ($successfulResults === 0) {
            return new JsonResponse([
                'status' => 'Post items not updated',
                'data'   => $result
            ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY, $headers = [], $json = false);
        }

        return new JsonResponse([
            'status' => 'Some post items not updated',
            'data'   => $result
        ], JsonResponse::HTTP_PARTIAL_CONTENT, $headers = [], $json = false);
    }

    /**
     * API endpoint that sets a group of Post Item status as released.
     *
     * @Route("/api/set-post-item-as-released", methods={"PUT"}, name="set_post_item_as_released")
     */
    public function setPostItemAsReleased(Request $request): JsonResponse
    {
        $body = json_decode($request->getContent(), true);

        if ($request->getMethod() != "PUT") {
            return new JsonResponse([
                'status' => 'Method not allowed',
            ], JsonResponse::HTTP_METHOD_NOT_ALLOWED);
        }

        if (empty($body)) {
            return new JsonResponse([
                'status' => 'Bad request',
            ], JsonResponse::HTTP_BAD_REQUEST);
        }

        $result = [];
        foreach ($body as $item) {
            if (!isset($item['post_item_id']) || !array_key_exists('status_to_set', $item)) {
                return new JsonResponse(
                    [
                        'status' => 'Request malformed. Missing either \'post_item_id\' or \'status_to_set\' keys in one of the post items received.',
                    ],
                    JsonResponse::HTTP_BAD_REQUEST
                );
            }

            $id = $item['post_item_id'];

            $postItem = $this->doctrine->getRepository(PostItem::class)->findOneBy(['id' => $id]);

            if (!$postItem) {
                $result[$id] = 0;
                continue;
            }

            if (!is_null($item['status_to_set'])) {
                $postItem->setStatus($item['status_to_set']);
                $this->doctrine->getManager()->persist($postItem);
                $this->doctrine->getManager()->flush();
            }

            $eventLog = new PostItemEventLog($this->doctrine->getManager(), $postItem);
            $eventLog->save(
                'released',
                'CMS'
            );

            if ($eventLog->hasAnyError()) {
                $result[$id] = 0;
            }

            $result[$id] = 1;
        }

        return new JsonResponse(
            [
                'status' => 'Post Item status updated',
                'result' => $result,
            ],
             JsonResponse::HTTP_OK
        );
    }

    /**
     * API that changes the status of one or more items
     *
     * @Route("/api/change-item-status", methods={"PUT"}, name="change_item_status")
     */
    public function changeItemStatus(Request $request)
    {
        $body = json_decode($request->getContent(), true);

        if ($request->getMethod() != "PUT") {
            return new JsonResponse([
                'status' => 'Method not allowed',
            ], JsonResponse::HTTP_METHOD_NOT_ALLOWED, $headers = [], $json = false);
        }

        if (empty($body)) {
            return new JsonResponse([
                'status' => 'Bad request',
            ], JsonResponse::HTTP_BAD_REQUEST, $headers = [], $json = false);
        }

        if (empty($postItemIds = $body['post_item_ids'])) {
            return new JsonResponse([
                'status' => 'Post Item IDs not provided',
            ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY, $headers = [], $json = false);
        }

        /** @var PostItemRepository $postItemRepository */
        $postItemRepository = $this->doctrine->getRepository(PostItem::class);

        $result = [];

        foreach ($postItemIds as $id => $status) {
            $postItem = $postItemRepository->findOneById($id);

            if (!$postItem) {
                $result[$id] = 0;
                continue;
            }

            try {
                $postItem->setStatus($status);

                $eventLog = new PostItemEventLog($this->doctrine->getManager(), $postItem);
                $eventLog->save(
                    eventName: sprintf('Item status changed to "%s"', $status),
                    operator: 'CMS',
                );

                $this->doctrine->getManager()->persist($postItem);
                $this->doctrine->getManager()->flush();
            } catch (\Exception) {
                $result[$id] = 0;
                continue;
            }

            $result[$id] = 1;
        }

        return new JsonResponse($result);
    }

    /**
     * Get a report on the Mailroom AI performance for a given month.
     *
     * @Route("/api/get-ai-report", methods={"GET", "OPTIONS"}, name="get_ai_report")
     */
    public function getAiReport(Request $request): JsonResponse
    {
        if ($request->getMethod() == "OPTIONS") {
            return new JsonResponse(null, JsonResponse::HTTP_NO_CONTENT, [
                'Allow' => 'GET, OPTIONS',
                'Access-Control-Allow-Methods' => 'GET, OPTIONS',
                'Access-Control-Allow-Headers' => 'Content-Type, Authorization',
                'Access-Control-Max-Age' => '3600'
            ]);
        }

        if ($request->getMethod() != "GET") {
            return new JsonResponse([
                'status' => 'Method not allowed',
            ], JsonResponse::HTTP_METHOD_NOT_ALLOWED, $headers = [], $json = false);
        }

        $dryRun = $request->get('dry-run', true);
        $debug = $request->get('debug', true);
        $noDiscord = $request->get('no-discord', false);
        $noDatabase = $request->get('no-database', false);

        $month = $request->get('month');
        $year = $request->get('year');

        $command = sprintf(
            'php ../bin/console app:report-ai-log%s%s%s%s%s%s',
            !!$dryRun ? ' --dry-run' : '',
            !!$debug ? ' --debug' : '',
            !!$noDiscord ? ' --no-discord' : '',
            !!$noDatabase ? ' --no-database' : '',
            !!$month ? ' --month=' . $month : '',
            !!$year ? ' --year=' . $year : ''
        );

        $process = Process::fromShellCommandline($command);

        try{
            $process->mustRun();
            $output = $process->getOutput();

            return new JsonResponse([
                'status' => 'success',
                'output' => $output,
            ], JsonResponse::HTTP_OK);
        } catch (ProcessFailedException $exception) {
            return new JsonResponse([
                'status' => 'error',
                'output' => $exception->getMessage(),
            ], JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        }

    }

    private function insertNewMessagesIntoDatabase(array $messages, StatusMessageRepository $statusMessageRepository)
    {
        foreach ($messages as $message) {
            $data = json_decode($message->data(), true);
            unset($data['message']);
            if (isset($data['pdf_filename']) && !empty($this->storage->getFile($data['pdf_filename'])->exists())) {
                $statusMessage = new StatusMessage();
                $statusMessage->setData(array_merge(
                    $data,
                    [
                        "message" => serialize($message),
                        "publishTime" => $message->publishTime()
                    ]
                ));
                if (isset($data['operator'])) {
                    $statusMessage->setOperator($data['operator']);
                    $statusMessage->setDtc(new DateTime('now', new DateTimeZone('UTC')));
                    $statusMessage->setDtm(new DateTime('now', new DateTimeZone('UTC')));
                    $statusMessageRepository->save($statusMessage);
                }
            }
        }
    }

    private function getUidMatchingMessages(array $messages, $uidToCompare, $batchIds, bool $matching = true)
    {
        $matchingMessages = [];
        $notMatchingMessages = [];

        foreach ($messages as $message){
            $data = json_decode($message->data(), true);
            if ($data['operator'] == $uidToCompare && in_array($data['batchId'], $batchIds)) {
                $matchingMessages[] = $message;
            } else {
                $notMatchingMessages[] = $message;
            }
        }

        return $matching ? $matchingMessages : $notMatchingMessages;
    }

    private function updateStatusMessageDatabase($uid, $batchIds, StatusMessageRepository $statusMessageRepository)
    {
        $statusMessageRepository->removeExpiredMessages();

        $newMessages = $this->pubSub->pullFromUiStatus();
        if ($newMessages) {
            $desiredMessages = $this->getUidMatchingMessages($newMessages, $uid, $batchIds);
            $undesiredMessages = $this->getUidMatchingMessages($newMessages, $uid, $batchIds, false);

            $this->insertNewMessagesIntoDatabase($desiredMessages, $statusMessageRepository);
            $this->pubSub->ackMessages($newMessages, Topic::uiStatus());
            $this->pubSub->rePublishMessagesToUiStatus($undesiredMessages);
        }
    }

    private function formatMessages(array $messages)
    {
        $formattedMessages = [];

        foreach ($messages as $message) {
            $formattedMessages[] = $message->toArray();
        }

        return $formattedMessages;
    }

    private function buildApiPostItemListData(array $postItemList)
    {
        $data = [];
        foreach($postItemList as $postItem){
            $detailList = [];
            foreach ($postItem->getDetails() as $detail) {
                $detailList[$detail->getKey()] = $detail->getValue();
            }

            $eventList = [];
            foreach ($postItem->getEvents() as $event) {
                $eventList[] = [
                    'event_name' => $event->getEventName(),
                    'operator' => $event->getOperator(),
                    'dtc' => $event->getDtc(),
                ];
            }

            $data[] = [
                "post_item_id" => $postItem->getId(),
                "status" => $postItem->getStatus(),
                "company_number" => $postItem->getCompanyNumber(),
                "company_name" => $postItem->getCompanyName(),
                "type" => $postItem->getType(),
                "sender" => $postItem->getSender(),
                "file_name" => $postItem->getFileName(),
                "batch_number" => $postItem->getBatchNumber(),
                "operator" => $postItem->getOperator(),
                "transaction_id" => $postItem->getTransactionId(),
                "dtc" => $postItem->getDtc(),
                "details" => $detailList,
                "events" => $eventList,
            ];
        }
        return $data;
    }

    private function buildApiPostItemData(PostItem $postItem): array
    {
        $detailList = [];
        foreach ($postItem->getDetails() as $detail) {
            $detailList[$detail->getKey()] = $detail->getValue();
        }

        $eventList = [];
        foreach ($postItem->getEvents() as $event) {
            $eventList[] = [
                'event_name' => $event->getEventName(),
                'operator' => $event->getOperator(),
                'dtc' => $event->getDtc(),
            ];
        }

        return [
            "post_item_id" => $postItem->getId(),
            "status" => $postItem->getStatus(),
            "company_number" => $postItem->getCompanyNumber(),
            "company_name" => $postItem->getCompanyName(),
            "type" => $postItem->getType(),
            "sender" => $postItem->getSender(),
            "file_name" => $postItem->getFileName(),
            "batch_number" => $postItem->getBatchNumber(),
            "operator" => $postItem->getOperator(),
            "transaction_id" => $postItem->getTransactionId(),
            "dtc" => $postItem->getDtc(),
            "details" => $detailList,
            "events" => $eventList,
        ];
    }

    private function setPostItemOrderIds(array $postItems, $doctrine): array
    {
        /** @var PostItemRepository $itemRepository */
        $itemRepository = $doctrine->getRepository(PostItem::class);

        /** @var PostItemsDetailRepository $detailRepository */
        $detailRepository = $doctrine->getRepository(PostItemsDetail::class);

        $results = [];
        foreach ($postItems as $postItemId => $orderId) {
            $postItem = $itemRepository->findOneBy(["id" => $postItemId]);
            if (!$postItem) {
                $results[$postItemId] = 0;
                continue;
            }

            /** @var PostItemsDetail $orderIdDetail */
            $orderIdDetail = $detailRepository->findOneBy(
                [
                    'postItemId' => $postItemId,
                    'key' => self::ORDER_ID_DEFAULT_KEY
                ],
                ['dtc' => 'DESC']
            );

            if (!!$orderIdDetail) {
                $orderIdDetail->setValue($orderId);
                $detailRepository->saveDetail($orderIdDetail);
            } else {
                $orderIdDetail = new PostItemsDetail();
                $orderIdDetail
                    ->setPostItemId($postItem)
                    ->setKey(self::ORDER_ID_DEFAULT_KEY)
                    ->setValue($orderId)
                ;
                $detailRepository->saveDetail($orderIdDetail);
            }

            $results[$postItemId] = 1;
        }

        return $results;
    }

    private function setPostItemLastEmailSent($postItemId, $emailId, $doctrine): array
    {
        /** @var PostItemRepository $itemRepository */
        $itemRepository = $doctrine->getRepository(PostItem::class);

        /** @var PostItemsDetailRepository $detailRepository */
        $detailRepository = $doctrine->getRepository(PostItemsDetail::class);

        $postItem = $itemRepository->findOneBy(["id" => $postItemId]);
        if (!$postItem) {
            return [];
        }

        /** @var PostItemsDetail $emailIdDetail */
        $emailIdDetail = $detailRepository->findOneBy(
            [
                'postItemId' => $postItemId,
                'key' => self::LAST_EMAIL_SENT_DEFAULT_KEY
            ],
            ['dtc' => 'DESC']
        );


        if (!!$emailIdDetail) {
            $emailIdDetail->setValue($emailId);
            $detailRepository->saveDetail($emailIdDetail);
            return [
                'status' => 'Post Item updated',
                'result' => 1
            ];
        }

        $emailIdDetail = new PostItemsDetail();
        $emailIdDetail
            ->setPostItemId($postItem)
            ->setKey(self::LAST_EMAIL_SENT_DEFAULT_KEY)
            ->setValue($emailId)
        ;
        $detailRepository->saveDetail($emailIdDetail);

        return [
            'status' => 'Post Item updated',
            'result' => 1
        ];
    }

    private function logEmailSentEvent($postItemId, $emailId, $emailLogId, $doctrine): ?string
    {
        /** @var PostItemRepository $itemRepository */
        $itemRepository = $doctrine->getRepository(PostItem::class);

        $postItem = $itemRepository->findOneBy(["id" => $postItemId]);
        if (!$postItem) {
            return null;
        }

        $eventLog = new PostItemEventLog($doctrine->getManager(), $postItem);
        $eventName = sprintf('email_sent:_%s_with_log_id_%s', $emailId, $emailLogId);
        $eventLog->save(
            $eventName,
            'CMS'
        );

        return $eventName;
    }

    private function countSuccessfulResults(array $results): int
    {
        return array_reduce($results, function($carry, $result) {
            if ($result) {
                return $carry + 1;
            }

            return $carry;
        }, 0);
    }

    private function generateStatusMessageForOrderIdOperation($succeededAmount, $totalAmount)
    {
        if ($succeededAmount == 0) {
            return sprintf('All %s Post Items failed to charge', $totalAmount);
        } else if ($succeededAmount == $totalAmount) {
            return sprintf('All %s Post Items successfully charged', $totalAmount);
        }

        return sprintf(
            'Partial success. %s out of %s Post Items successfully charged',
            $succeededAmount,
            $totalAmount
        );
    }

    private function getPostItemsFromStatusList(
        ManagerRegistry $doctrine,
        array $statuses,
        ?int $limit,
        ?string $lastId
    ) {
        /** @var PostItemRepository $repository */
        $repository = $doctrine->getRepository(PostItem::class);
        if (is_null($limit)) {
            return $repository->getItemsFromStatusList($statuses);
        }

        return $repository->getChunkOfItemsFromStatusList($statuses, $lastId, $limit);
    }
}
